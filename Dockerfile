# DEGAP v2.0 Docker Container
# Alternative to Singularity for easier Windows compatibility

FROM ubuntu:20.04

# Metadata
LABEL maintainer="DEGAP Development Team"
LABEL version="2.0"
LABEL description="DEGAP v2.0 - Dynamic Elongation of a Genome Assembly Path"

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PATH="/opt/conda/bin:/opt/minimap2:/opt/hifiasm:/opt/samtools/bin:/opt/seqkit:/opt/mummer/bin:$PATH"
ENV PYTHONPATH="/opt/degap:$PYTHONPATH"
ENV LC_ALL=C

# Update system and install basic dependencies
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    git \
    build-essential \
    gcc \
    g++ \
    make \
    cmake \
    zlib1g-dev \
    libbz2-dev \
    liblzma-dev \
    libncurses5-dev \
    libcurl4-gnutls-dev \
    libssl-dev \
    python3 \
    python3-pip \
    python3-dev \
    unzip \
    bzip2 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Miniconda for Python package management
RUN wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh && \
    bash /tmp/miniconda.sh -b -p /opt/conda && \
    rm /tmp/miniconda.sh

# Update conda and install Python packages
RUN /opt/conda/bin/conda update -n base -c defaults conda && \
    /opt/conda/bin/conda install -c conda-forge -c bioconda \
        python=3.9 \
        biopython=1.80 \
        pysam=0.20.0 \
        matplotlib \
        numpy \
        scipy

# Install minimap2 (version 2.17-r941)
RUN cd /opt && \
    wget https://github.com/lh3/minimap2/releases/download/v2.17/minimap2-2.17_x64-linux.tar.bz2 && \
    tar -xjf minimap2-2.17_x64-linux.tar.bz2 && \
    mv minimap2-2.17_x64-linux minimap2 && \
    rm minimap2-2.17_x64-linux.tar.bz2

# Install hifiasm (version 0.16.1-r375)
RUN cd /opt && \
    git clone https://github.com/chhylp123/hifiasm.git && \
    cd hifiasm && \
    git checkout 0.16.1 && \
    make

# Install SAMtools (version 1.6)
RUN cd /opt && \
    wget https://github.com/samtools/samtools/releases/download/1.6/samtools-1.6.tar.bz2 && \
    tar -xjf samtools-1.6.tar.bz2 && \
    cd samtools-1.6 && \
    ./configure --prefix=/opt/samtools && \
    make && make install && \
    cd /opt && \
    rm samtools-1.6.tar.bz2

# Install seqkit (version 2.8.0)
RUN cd /opt && \
    wget https://github.com/shenwei356/seqkit/releases/download/v2.8.0/seqkit_linux_amd64.tar.gz && \
    tar -xzf seqkit_linux_amd64.tar.gz && \
    mkdir -p seqkit && \
    mv seqkit seqkit/ && \
    rm seqkit_linux_amd64.tar.gz

# Install MUMmer (version 4.0.0beta2)
RUN cd /opt && \
    wget https://github.com/mummer4/mummer/releases/download/v4.0.0beta2/mummer-4.0.0beta2.tar.gz && \
    tar -xzf mummer-4.0.0beta2.tar.gz && \
    cd mummer-4.0.0beta2 && \
    ./configure --prefix=/opt/mummer && \
    make && make install && \
    cd /opt && \
    rm mummer-4.0.0beta2.tar.gz

# Create DEGAP directory
RUN mkdir -p /opt/degap

# Copy DEGAP source files
COPY AutoGapfiller.py /opt/degap/
COPY CtgLinker.py /opt/degap/
COPY DEGAP.py /opt/degap/
COPY FindExtensionContigs.py /opt/degap/
COPY FindExtensionReads.py /opt/degap/
COPY GapFiller.py /opt/degap/
COPY GapFillerClass.py /opt/degap/
COPY N50Check.py /opt/degap/
COPY README.md /opt/degap/
COPY TelSeeker.py /opt/degap/
COPY selectRawReads.py /opt/degap/
COPY DEGAP.png /opt/degap/

# Set working directory
WORKDIR /opt/degap

# Clean up
RUN apt-get clean && \
    /opt/conda/bin/conda clean --all -y

# Default command
CMD ["python3", "/opt/degap/DEGAP.py", "--help"]
