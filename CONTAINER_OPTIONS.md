# DEGAP v2.0 容器化选项总结

本文档总结了为 DEGAP v2.0 项目提供的所有容器化选项，帮助您选择最适合的方案。

## 可用选项

### 1. Singularity 容器（推荐用于 HPC 环境）

**优势：**
- 专为科学计算和 HPC 环境设计
- 更好的安全性和权限管理
- 原生支持 GPU 和 MPI
- 不需要 root 权限运行

**文件：**
- `degap.def` - Singularity 定义文件
- `build_degap_container.sh` - Linux/macOS 构建脚本
- `build_degap_container.ps1` - Windows PowerShell 构建脚本

**适用场景：**
- 高性能计算集群
- 科研环境
- 需要严格安全控制的环境

### 2. Docker 容器（推荐用于个人和开发环境）

**优势：**
- 广泛支持，易于安装
- 丰富的生态系统
- 优秀的 Windows 支持
- 简单的镜像分发

**文件：**
- `Dockerfile` - Docker 构建文件
- `build_docker_container.ps1` - Windows PowerShell 构建脚本

**适用场景：**
- 个人开发环境
- Windows 用户
- 快速原型和测试
- CI/CD 流水线

## 系统兼容性

| 操作系统 | Singularity | Docker |
|----------|-------------|--------|
| Linux | ✅ 原生支持 | ✅ 原生支持 |
| macOS | ✅ 通过 VM | ✅ Docker Desktop |
| Windows | ⚠️ 需要 WSL2 | ✅ Docker Desktop |

## 快速开始指南

### 选择 Singularity（Linux/HPC 用户）

1. **构建容器：**
```bash
# Linux/macOS
chmod +x build_degap_container.sh
./build_degap_container.sh build

# Windows (在 WSL2 中)
wsl ./build_degap_container.sh build
```

2. **使用容器：**
```bash
singularity exec --bind /your/data:/data degap.sif \
    python /opt/degap/DEGAP.py --mode gapfiller \
    --seqleft /data/left.fasta \
    --seqright /data/right.fasta \
    --reads /data/reads.fasta \
    -o /data/output
```

### 选择 Docker（Windows/个人用户）

1. **构建容器：**
```powershell
# Windows PowerShell
.\build_docker_container.ps1 build
```

2. **使用容器：**
```powershell
docker run --rm -v "C:\your\data:/data" degap:v2.0 `
    python /opt/degap/DEGAP.py --mode gapfiller `
    --seqleft /data/left.fasta `
    --seqright /data/right.fasta `
    --reads /data/reads.fasta `
    -o /data/output
```

## 性能对比

| 特性 | Singularity | Docker |
|------|-------------|--------|
| 启动速度 | 快 | 中等 |
| 资源开销 | 低 | 中等 |
| 安全性 | 高 | 中等 |
| HPC 集成 | 优秀 | 一般 |
| Windows 支持 | 需要 WSL2 | 原生 |

## 构建时间和资源需求

### 构建时间
- **首次构建：** 15-30 分钟（取决于网络速度）
- **重新构建：** 5-10 分钟（利用缓存）

### 资源需求
- **磁盘空间：** 约 3-4 GB
- **内存：** 构建时需要至少 4 GB
- **网络：** 需要下载约 1-2 GB 的依赖

## 故障排除

### Singularity 常见问题

1. **权限错误**
```bash
# 解决方案：使用 sudo 构建
sudo singularity build degap.sif degap.def
```

2. **WSL2 中的路径问题**
```bash
# 使用 WSL2 路径格式
singularity exec --bind /mnt/c/data:/data degap.sif ...
```

### Docker 常见问题

1. **Docker 守护进程未运行**
```powershell
# 启动 Docker Desktop
# 或在 PowerShell 中检查
docker info
```

2. **Windows 路径挂载问题**
```powershell
# 使用正确的 Windows 路径格式
docker run -v "C:\Users\<USER>\data:/data" degap:v2.0 ...
```

## 选择建议

### 推荐使用 Singularity 如果：
- 您在 Linux 环境中工作
- 需要在 HPC 集群上运行
- 对安全性有严格要求
- 需要与现有的科学计算工作流集成

### 推荐使用 Docker 如果：
- 您使用 Windows 系统
- 需要快速开始和测试
- 熟悉 Docker 生态系统
- 需要与 CI/CD 流水线集成

## 技术支持

如果遇到问题：

1. **检查系统要求**
   - 确保有足够的磁盘空间和内存
   - 验证容器运行时已正确安装

2. **查看日志**
   - Singularity: 检查构建输出
   - Docker: 使用 `docker logs` 命令

3. **社区支持**
   - Singularity: https://sylabs.io/docs/
   - Docker: https://docs.docker.com/
   - DEGAP: 联系 <EMAIL>

## 更新和维护

### 更新容器
当 DEGAP 代码更新时：
1. 更新源代码文件
2. 重新运行构建脚本
3. 测试新容器功能

### 版本管理
```bash
# Singularity
singularity build degap_v2.1.sif degap.def

# Docker
docker build -t degap:v2.1 .
docker tag degap:v2.1 degap:latest
```

## 总结

两种容器化方案都能很好地满足 DEGAP v2.0 的运行需求。选择哪种方案主要取决于您的具体使用环境和需求。对于大多数科研用户，我们推荐使用 Singularity；对于 Windows 用户和快速测试场景，Docker 是更好的选择。
