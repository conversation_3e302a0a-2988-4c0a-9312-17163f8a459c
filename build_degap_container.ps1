# DEGAP v2.0 Singularity Container Build Script for Windows
# PowerShell version

param(
    [Parameter(Position=0)]
    [ValidateSet("build", "test", "usage", "help")]
    [string]$Command = "build"
)

# Colors for output (Windows PowerShell compatible)
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Singularity is installed
function Test-Singularity {
    Write-Status "Checking Singularity installation..."
    
    try {
        $version = & singularity --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Found Singularity: $version"
            return $true
        }
    }
    catch {
        # Singularity not found
    }
    
    Write-Error "Singularity is not installed or not in PATH"
    Write-Status "Please install Singularity first:"
    Write-Status "For Windows, you can use:"
    Write-Status "1. WSL2 with Singularity installed in Linux"
    Write-Status "2. Docker Desktop with Singularity"
    Write-Status "3. Virtual Machine with Linux"
    Write-Status "Visit: https://sylabs.io/guides/3.0/user-guide/installation.html"
    return $false
}

# Check if definition file exists
function Test-DefinitionFile {
    Write-Status "Checking definition file..."
    if (-not (Test-Path "degap.def")) {
        Write-Error "Definition file 'degap.def' not found in current directory"
        return $false
    }
    Write-Success "Found definition file: degap.def"
    return $true
}

# Check if all DEGAP source files exist
function Test-SourceFiles {
    Write-Status "Checking DEGAP source files..."
    
    $requiredFiles = @(
        "AutoGapfiller.py",
        "CtgLinker.py", 
        "DEGAP.py",
        "FindExtensionContigs.py",
        "FindExtensionReads.py",
        "GapFiller.py",
        "GapFillerClass.py",
        "N50Check.py",
        "README.md",
        "TelSeeker.py",
        "selectRawReads.py",
        "DEGAP.png"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-Error "Missing required source files:"
        foreach ($file in $missingFiles) {
            Write-Host "  - $file" -ForegroundColor Red
        }
        return $false
    }
    
    Write-Success "All required source files found"
    return $true
}

# Build the container
function Build-Container {
    Write-Status "Building DEGAP Singularity container..."
    Write-Warning "This may take 15-30 minutes depending on your internet connection"
    
    try {
        Write-Status "Running: singularity build degap.sif degap.def"
        & singularity build degap.sif degap.def
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Container built successfully: degap.sif"
            return $true
        } else {
            Write-Error "Container build failed with exit code: $LASTEXITCODE"
            return $false
        }
    }
    catch {
        Write-Error "Container build failed: $($_.Exception.Message)"
        return $false
    }
}

# Test the container
function Test-Container {
    Write-Status "Testing the container..."
    
    if (-not (Test-Path "degap.sif")) {
        Write-Error "Container file 'degap.sif' not found"
        return $false
    }
    
    try {
        Write-Status "Running container tests..."
        & singularity test degap.sif
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Container tests passed"
            return $true
        } else {
            Write-Error "Container tests failed"
            return $false
        }
    }
    catch {
        Write-Error "Container test failed: $($_.Exception.Message)"
        return $false
    }
}

# Show usage examples
function Show-Usage {
    Write-Success "DEGAP Container Usage Examples:"
    Write-Host ""
    Write-Host "1. GapFiller mode:" -ForegroundColor Cyan
    Write-Host "   singularity exec degap.sif python /opt/degap/DEGAP.py --mode gapfiller \"
    Write-Host "       --seqleft /path/to/left.fasta \"
    Write-Host "       --seqright /path/to/right.fasta \"
    Write-Host "       --reads /path/to/reads.fasta \"
    Write-Host "       -o /path/to/output \"
    Write-Host "       --flag left"
    Write-Host ""
    Write-Host "2. CtgLinker mode:" -ForegroundColor Cyan
    Write-Host "   singularity exec degap.sif python /opt/degap/DEGAP.py --mode ctglinker \"
    Write-Host "       --ctgseq /path/to/contigs.fasta \"
    Write-Host "       --reads /path/to/reads.fasta \"
    Write-Host "       --out /path/to/output \"
    Write-Host "       --filterDepth 0.3"
    Write-Host ""
    Write-Host "3. TelSeeker mode:" -ForegroundColor Cyan
    Write-Host "   singularity exec degap.sif python /opt/degap/DEGAP.py --mode telseeker \"
    Write-Host "       --reads /path/to/reads.fasta \"
    Write-Host "       --seqleft /path/to/start.fasta \"
    Write-Host "       --seqright /path/to/end.fasta \"
    Write-Host "       --flag left \"
    Write-Host "       -o /path/to/output"
    Write-Host ""
    Write-Host "4. AutoGapfiller batch processing:" -ForegroundColor Cyan
    Write-Host "   singularity exec degap.sif python /opt/degap/AutoGapfiller.py \"
    Write-Host "       --reads /path/to/reads.fasta \"
    Write-Host "       --genome /path/to/genome.fasta \"
    Write-Host "       --mode gapfiller \"
    Write-Host "       --flag all \"
    Write-Host "       -o /path/to/output \"
    Write-Host "       --batch 10"
    Write-Host ""
    Write-Host "5. Get help:" -ForegroundColor Cyan
    Write-Host "   singularity exec degap.sif python /opt/degap/DEGAP.py --help"
    Write-Host ""
    Write-Status "Note: Make sure to bind mount your data directories when running:"
    Write-Host "   singularity exec --bind /your/data/path:/data degap.sif python /opt/degap/DEGAP.py ..."
    Write-Host ""
    Write-Status "For Windows users using WSL2:"
    Write-Host "   wsl singularity exec --bind /mnt/c/your/data:/data degap.sif python /opt/degap/DEGAP.py ..."
}

# Main execution
function Main {
    Write-Host "========================================" -ForegroundColor Magenta
    Write-Host "DEGAP v2.0 Singularity Container Builder" -ForegroundColor Magenta
    Write-Host "PowerShell Version for Windows" -ForegroundColor Magenta
    Write-Host "========================================" -ForegroundColor Magenta
    Write-Host ""
    
    switch ($Command) {
        "build" {
            if (-not (Test-Singularity)) { exit 1 }
            if (-not (Test-DefinitionFile)) { exit 1 }
            if (-not (Test-SourceFiles)) { exit 1 }
            if (-not (Build-Container)) { exit 1 }
            if (-not (Test-Container)) { exit 1 }
            Show-Usage
        }
        "test" {
            if (-not (Test-Singularity)) { exit 1 }
            if (-not (Test-Container)) { exit 1 }
        }
        "usage" {
            Show-Usage
        }
        "help" {
            Write-Host "Usage: .\build_degap_container.ps1 [build|test|usage|help]" -ForegroundColor Yellow
            Write-Host ""
            Write-Host "Commands:" -ForegroundColor Yellow
            Write-Host "  build   - Build the DEGAP container (default)"
            Write-Host "  test    - Test an existing container"
            Write-Host "  usage   - Show usage examples"
            Write-Host "  help    - Show this help message"
            Write-Host ""
            Write-Host "Examples:" -ForegroundColor Yellow
            Write-Host "  .\build_degap_container.ps1"
            Write-Host "  .\build_degap_container.ps1 build"
            Write-Host "  .\build_degap_container.ps1 test"
            Write-Host "  .\build_degap_container.ps1 usage"
        }
        default {
            Write-Error "Unknown command: $Command"
            Write-Host "Use '.\build_degap_container.ps1 help' for usage information"
            exit 1
        }
    }
}

# Run main function
Main
