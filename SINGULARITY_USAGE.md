# DEGAP v2.0 Singularity Container 使用指南

## 概述

本文档介绍如何使用 Singularity 将 DEGAP v2.0 项目打包为容器镜像，以及如何使用该容器。

## 文件说明

- `degap.def`: Singularity 定义文件，包含容器构建指令
- `build_degap_container.sh`: Linux/macOS 自动化构建脚本
- `build_degap_container.ps1`: Windows PowerShell 构建脚本
- `requirements.txt`: Python 依赖列表
- `SINGULARITY_USAGE.md`: 本使用指南

## 系统要求

### 主机系统要求
- Linux 操作系统（推荐 Ubuntu 18.04+ 或 CentOS 7+）
- 或 Windows 10/11 with WSL2
- Singularity 3.0+ 已安装
- 至少 8GB 可用磁盘空间
- 管理员权限（构建时需要）

### Singularity 安装

#### Linux 系统
如果尚未安装 Singularity，请参考官方文档：
https://sylabs.io/guides/3.0/user-guide/installation.html

Ubuntu/Debian 快速安装：
```bash
sudo apt update
sudo apt install -y singularity-container
```

#### Windows 系统
Windows 用户有以下几种选择：

1. **WSL2 + Singularity（推荐）**
```powershell
# 安装 WSL2
wsl --install

# 在 WSL2 中安装 Singularity
wsl
sudo apt update
sudo apt install -y singularity-container
```

2. **Docker Desktop + Singularity**
- 安装 Docker Desktop
- 在 Docker 容器中运行 Singularity

3. **虚拟机**
- 使用 VirtualBox 或 VMware 安装 Linux 虚拟机

## 构建容器

### 1. 准备文件
确保所有 DEGAP 源文件都在当前目录中：
```bash
ls -la
# 应该看到以下文件：
# AutoGapfiller.py, CtgLinker.py, DEGAP.py, FindExtensionContigs.py
# FindExtensionReads.py, GapFiller.py, GapFillerClass.py, N50Check.py
# README.md, TelSeeker.py, selectRawReads.py, DEGAP.png
# degap.def, build_degap_container.sh
```

### 2. 运行构建脚本

#### Linux/macOS 系统
```bash
# 给构建脚本执行权限
chmod +x build_degap_container.sh

# 运行构建（需要 sudo 权限）
./build_degap_container.sh build
```

#### Windows 系统
```powershell
# 在 PowerShell 中运行
.\build_degap_container.ps1 build

# 或者在 WSL2 中运行 Linux 版本
wsl ./build_degap_container.sh build
```

### 3. 手动构建（可选）
如果不使用构建脚本，也可以手动构建：
```bash
sudo singularity build degap.sif degap.def
```

## 容器使用

### 基本语法
```bash
singularity exec [选项] degap.sif python /opt/degap/DEGAP.py [DEGAP参数]
```

### 数据绑定
使用 `--bind` 选项将主机目录挂载到容器中：
```bash
singularity exec --bind /path/to/your/data:/data degap.sif python /opt/degap/DEGAP.py ...
```

### 使用示例

#### 1. GapFiller 模式
```bash
singularity exec --bind /home/<USER>/data:/data degap.sif \
    python /opt/degap/DEGAP.py --mode gapfiller \
    --seqleft /data/left_sequence.fasta \
    --seqright /data/right_sequence.fasta \
    --reads /data/hifi_reads.fasta \
    -o /data/output \
    --flag left \
    -t 20
```

#### 2. CtgLinker 模式
```bash
singularity exec --bind /home/<USER>/data:/data degap.sif \
    python /opt/degap/DEGAP.py --mode ctglinker \
    --ctgseq /data/contigs.fasta \
    --reads /data/hifi_reads.fasta \
    --out /data/output \
    --filterDepth 0.3 \
    -t 20
```

#### 3. TelSeeker 模式
```bash
singularity exec --bind /home/<USER>/data:/data degap.sif \
    python /opt/degap/DEGAP.py --mode telseeker \
    --reads /data/hifi_reads.fasta \
    --seqleft /data/start_sequence.fasta \
    --seqright /data/end_sequences.fasta \
    --flag left \
    -o /data/output
```

#### 4. AutoGapfiller 批处理
```bash
singularity exec --bind /home/<USER>/data:/data degap.sif \
    python /opt/degap/AutoGapfiller.py \
    --reads /data/hifi_reads.fasta \
    --genome /data/genome.fasta \
    --mode gapfiller \
    --flag all \
    -o /data/output \
    --batch 10
```

### 获取帮助
```bash
# DEGAP 帮助
singularity exec degap.sif python /opt/degap/DEGAP.py --help

# AutoGapfiller 帮助
singularity exec degap.sif python /opt/degap/AutoGapfiller.py --help
```

## 容器内包含的工具

容器内预装了以下工具和版本：

### Python 包
- Python 3.9
- Biopython 1.80
- Pysam 0.20.0
- NumPy, SciPy, Matplotlib

### 生物信息学工具
- Minimap2 2.17-r941
- Hifiasm 0.16.1-r375
- SAMtools 1.6
- Seqkit 2.8.0
- MUMmer 4.0.0beta2

## 测试容器

### 运行内置测试
```bash
singularity test degap.sif
```

### 或使用构建脚本测试
```bash
./build_degap_container.sh test
```

## 性能优化建议

### 1. 内存和CPU
- 容器会继承主机的资源限制
- 使用 `-t` 参数控制线程数
- 使用 `-j` 参数控制并行作业数

### 2. 存储优化
- 使用 `--bind` 挂载大数据目录
- 考虑使用 `--remove` 参数控制中间文件保留

### 3. 集群使用
- 容器可以在 SLURM、PBS 等作业调度系统中使用
- 示例 SLURM 脚本：
```bash
#!/bin/bash
#SBATCH --job-name=degap
#SBATCH --cpus-per-task=20
#SBATCH --mem=32G
#SBATCH --time=24:00:00

module load singularity

singularity exec --bind $PWD:/data degap.sif \
    python /opt/degap/DEGAP.py --mode gapfiller \
    --seqleft /data/left.fasta \
    --seqright /data/right.fasta \
    --reads /data/reads.fasta \
    -o /data/output \
    -t 20
```

## 故障排除

### 常见问题

1. **权限错误**
   - 确保使用 sudo 构建容器
   - 检查数据目录的读写权限

2. **内存不足**
   - 减少并行作业数 (`-j` 参数)
   - 使用数据过滤选项

3. **文件路径问题**
   - 确保使用 `--bind` 正确挂载数据目录
   - 使用容器内的绝对路径

4. **工具版本问题**
   - 容器内的工具版本是固定的
   - 如需不同版本，需要修改 `degap.def` 文件

### 调试模式
```bash
# 进入容器 shell 进行调试
singularity shell --bind /your/data:/data degap.sif

# 在容器内检查环境
Singularity> which python3
Singularity> python3 -c "import Bio; print(Bio.__version__)"
Singularity> ls /opt/degap/
```

## 容器分发

构建完成的 `degap.sif` 文件可以：
- 复制到其他机器直接使用
- 上传到容器仓库（如 Singularity Hub）
- 与团队成员共享

## 更新和维护

如需更新容器：
1. 修改 `degap.def` 文件
2. 重新运行构建脚本
3. 测试新容器功能

## 支持

如有问题，请：
1. 检查本文档的故障排除部分
2. 查看 DEGAP 原始文档
3. 联系 DEGAP 开发团队：<EMAIL>
