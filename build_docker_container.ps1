# DEGAP v2.0 Docker Container Build Script for Windows
# PowerShell version - Alternative to Singularity

param(
    [Parameter(Position=0)]
    [ValidateSet("build", "test", "usage", "help", "run")]
    [string]$Command = "build"
)

# Colors for output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Docker is installed and running
function Test-Docker {
    Write-Status "Checking Docker installation..."
    
    try {
        $version = & docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Found Docker: $version"
            
            # Check if Docker daemon is running
            $info = & docker info 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Docker daemon is running"
                return $true
            } else {
                Write-Error "Docker daemon is not running. Please start Docker Desktop."
                return $false
            }
        }
    }
    catch {
        # Docker not found
    }
    
    Write-Error "Docker is not installed or not in PATH"
    Write-Status "Please install Docker Desktop first:"
    Write-Status "https://www.docker.com/products/docker-desktop"
    return $false
}

# Check if Dockerfile exists
function Test-Dockerfile {
    Write-Status "Checking Dockerfile..."
    if (-not (Test-Path "Dockerfile")) {
        Write-Error "Dockerfile not found in current directory"
        return $false
    }
    Write-Success "Found Dockerfile"
    return $true
}

# Check if all DEGAP source files exist
function Test-SourceFiles {
    Write-Status "Checking DEGAP source files..."
    
    $requiredFiles = @(
        "AutoGapfiller.py",
        "CtgLinker.py", 
        "DEGAP.py",
        "FindExtensionContigs.py",
        "FindExtensionReads.py",
        "GapFiller.py",
        "GapFillerClass.py",
        "N50Check.py",
        "README.md",
        "TelSeeker.py",
        "selectRawReads.py",
        "DEGAP.png"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-Error "Missing required source files:"
        foreach ($file in $missingFiles) {
            Write-Host "  - $file" -ForegroundColor Red
        }
        return $false
    }
    
    Write-Success "All required source files found"
    return $true
}

# Build the Docker container
function Build-DockerContainer {
    Write-Status "Building DEGAP Docker container..."
    Write-Warning "This may take 15-30 minutes depending on your internet connection"
    
    try {
        Write-Status "Running: docker build -t degap:v2.0 ."
        & docker build -t degap:v2.0 .
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker container built successfully: degap:v2.0"
            return $true
        } else {
            Write-Error "Docker container build failed with exit code: $LASTEXITCODE"
            return $false
        }
    }
    catch {
        Write-Error "Docker container build failed: $($_.Exception.Message)"
        return $false
    }
}

# Test the Docker container
function Test-DockerContainer {
    Write-Status "Testing the Docker container..."
    
    try {
        Write-Status "Running container tests..."
        
        # Test if container can run
        Write-Status "Testing basic container functionality..."
        & docker run --rm degap:v2.0 python3 -c "import Bio; print('Biopython version:', Bio.__version__)"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Container basic test passed"
        } else {
            Write-Error "Container basic test failed"
            return $false
        }
        
        # Test DEGAP help
        Write-Status "Testing DEGAP help command..."
        & docker run --rm degap:v2.0 python3 /opt/degap/DEGAP.py --help
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "DEGAP help test passed"
            return $true
        } else {
            Write-Error "DEGAP help test failed"
            return $false
        }
    }
    catch {
        Write-Error "Container test failed: $($_.Exception.Message)"
        return $false
    }
}

# Show usage examples
function Show-DockerUsage {
    Write-Success "DEGAP Docker Container Usage Examples:"
    Write-Host ""
    Write-Host "1. GapFiller mode:" -ForegroundColor Cyan
    Write-Host "   docker run --rm -v `"C:\your\data:/data`" degap:v2.0 \"
    Write-Host "       python /opt/degap/DEGAP.py --mode gapfiller \"
    Write-Host "       --seqleft /data/left.fasta \"
    Write-Host "       --seqright /data/right.fasta \"
    Write-Host "       --reads /data/reads.fasta \"
    Write-Host "       -o /data/output \"
    Write-Host "       --flag left"
    Write-Host ""
    Write-Host "2. CtgLinker mode:" -ForegroundColor Cyan
    Write-Host "   docker run --rm -v `"C:\your\data:/data`" degap:v2.0 \"
    Write-Host "       python /opt/degap/DEGAP.py --mode ctglinker \"
    Write-Host "       --ctgseq /data/contigs.fasta \"
    Write-Host "       --reads /data/reads.fasta \"
    Write-Host "       --out /data/output \"
    Write-Host "       --filterDepth 0.3"
    Write-Host ""
    Write-Host "3. TelSeeker mode:" -ForegroundColor Cyan
    Write-Host "   docker run --rm -v `"C:\your\data:/data`" degap:v2.0 \"
    Write-Host "       python /opt/degap/DEGAP.py --mode telseeker \"
    Write-Host "       --reads /data/reads.fasta \"
    Write-Host "       --seqleft /data/start.fasta \"
    Write-Host "       --seqright /data/end.fasta \"
    Write-Host "       --flag left \"
    Write-Host "       -o /data/output"
    Write-Host ""
    Write-Host "4. Interactive shell:" -ForegroundColor Cyan
    Write-Host "   docker run --rm -it -v `"C:\your\data:/data`" degap:v2.0 bash"
    Write-Host ""
    Write-Host "5. Get help:" -ForegroundColor Cyan
    Write-Host "   docker run --rm degap:v2.0 python /opt/degap/DEGAP.py --help"
    Write-Host ""
    Write-Status "Note: Replace 'C:\your\data' with your actual data directory path"
    Write-Status "The -v option mounts your local directory to /data inside the container"
}

# Run a simple DEGAP command
function Run-DockerExample {
    Write-Status "Running a simple DEGAP example..."
    Write-Host "This will show the DEGAP help message:"
    Write-Host ""
    
    try {
        & docker run --rm degap:v2.0 python /opt/degap/DEGAP.py --help
        Write-Success "Example completed successfully"
    }
    catch {
        Write-Error "Example failed: $($_.Exception.Message)"
    }
}

# Main execution
function Main {
    Write-Host "========================================" -ForegroundColor Magenta
    Write-Host "DEGAP v2.0 Docker Container Builder" -ForegroundColor Magenta
    Write-Host "Windows PowerShell Version" -ForegroundColor Magenta
    Write-Host "========================================" -ForegroundColor Magenta
    Write-Host ""
    
    switch ($Command) {
        "build" {
            if (-not (Test-Docker)) { exit 1 }
            if (-not (Test-Dockerfile)) { exit 1 }
            if (-not (Test-SourceFiles)) { exit 1 }
            if (-not (Build-DockerContainer)) { exit 1 }
            if (-not (Test-DockerContainer)) { exit 1 }
            Show-DockerUsage
        }
        "test" {
            if (-not (Test-Docker)) { exit 1 }
            if (-not (Test-DockerContainer)) { exit 1 }
        }
        "usage" {
            Show-DockerUsage
        }
        "run" {
            if (-not (Test-Docker)) { exit 1 }
            Run-DockerExample
        }
        "help" {
            Write-Host "Usage: .\build_docker_container.ps1 [build|test|usage|run|help]" -ForegroundColor Yellow
            Write-Host ""
            Write-Host "Commands:" -ForegroundColor Yellow
            Write-Host "  build   - Build the DEGAP Docker container (default)"
            Write-Host "  test    - Test an existing container"
            Write-Host "  usage   - Show usage examples"
            Write-Host "  run     - Run a simple example"
            Write-Host "  help    - Show this help message"
            Write-Host ""
            Write-Host "Examples:" -ForegroundColor Yellow
            Write-Host "  .\build_docker_container.ps1"
            Write-Host "  .\build_docker_container.ps1 build"
            Write-Host "  .\build_docker_container.ps1 test"
            Write-Host "  .\build_docker_container.ps1 usage"
            Write-Host "  .\build_docker_container.ps1 run"
        }
        default {
            Write-Error "Unknown command: $Command"
            Write-Host "Use '.\build_docker_container.ps1 help' for usage information"
            exit 1
        }
    }
}

# Run main function
Main
