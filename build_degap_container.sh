#!/bin/bash

# DEGAP v2.0 Singularity Container Build Script
# This script helps build and test the DEGAP Singularity container

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Singularity is installed
check_singularity() {
    print_status "Checking Singularity installation..."
    if ! command -v singularity &> /dev/null; then
        print_error "Singularity is not installed or not in PATH"
        print_status "Please install Singularity first:"
        print_status "https://sylabs.io/guides/3.0/user-guide/installation.html"
        exit 1
    fi
    
    singularity_version=$(singularity --version)
    print_success "Found Singularity: $singularity_version"
}

# Check if definition file exists
check_definition_file() {
    print_status "Checking definition file..."
    if [ ! -f "degap.def" ]; then
        print_error "Definition file 'degap.def' not found in current directory"
        exit 1
    fi
    print_success "Found definition file: degap.def"
}

# Check if all DEGAP source files exist
check_source_files() {
    print_status "Checking DEGAP source files..."
    
    required_files=(
        "AutoGapfiller.py"
        "CtgLinker.py" 
        "DEGAP.py"
        "FindExtensionContigs.py"
        "FindExtensionReads.py"
        "GapFiller.py"
        "GapFillerClass.py"
        "N50Check.py"
        "README.md"
        "TelSeeker.py"
        "selectRawReads.py"
        "DEGAP.png"
    )
    
    missing_files=()
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        print_error "Missing required source files:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    print_success "All required source files found"
}

# Build the container
build_container() {
    print_status "Building DEGAP Singularity container..."
    print_warning "This may take 15-30 minutes depending on your internet connection"
    
    # Check if we need sudo
    if [ "$EUID" -ne 0 ]; then
        print_status "Building with sudo (required for Singularity build)..."
        sudo singularity build degap.sif degap.def
    else
        singularity build degap.sif degap.def
    fi
    
    if [ $? -eq 0 ]; then
        print_success "Container built successfully: degap.sif"
    else
        print_error "Container build failed"
        exit 1
    fi
}

# Test the container
test_container() {
    print_status "Testing the container..."
    
    if [ ! -f "degap.sif" ]; then
        print_error "Container file 'degap.sif' not found"
        exit 1
    fi
    
    print_status "Running container tests..."
    singularity test degap.sif
    
    if [ $? -eq 0 ]; then
        print_success "Container tests passed"
    else
        print_error "Container tests failed"
        exit 1
    fi
}

# Show usage examples
show_usage() {
    print_success "DEGAP Container Usage Examples:"
    echo ""
    echo "1. GapFiller mode:"
    echo "   singularity exec degap.sif python /opt/degap/DEGAP.py --mode gapfiller \\"
    echo "       --seqleft /path/to/left.fasta \\"
    echo "       --seqright /path/to/right.fasta \\"
    echo "       --reads /path/to/reads.fasta \\"
    echo "       -o /path/to/output \\"
    echo "       --flag left"
    echo ""
    echo "2. CtgLinker mode:"
    echo "   singularity exec degap.sif python /opt/degap/DEGAP.py --mode ctglinker \\"
    echo "       --ctgseq /path/to/contigs.fasta \\"
    echo "       --reads /path/to/reads.fasta \\"
    echo "       --out /path/to/output \\"
    echo "       --filterDepth 0.3"
    echo ""
    echo "3. TelSeeker mode:"
    echo "   singularity exec degap.sif python /opt/degap/DEGAP.py --mode telseeker \\"
    echo "       --reads /path/to/reads.fasta \\"
    echo "       --seqleft /path/to/start.fasta \\"
    echo "       --seqright /path/to/end.fasta \\"
    echo "       --flag left \\"
    echo "       -o /path/to/output"
    echo ""
    echo "4. AutoGapfiller batch processing:"
    echo "   singularity exec degap.sif python /opt/degap/AutoGapfiller.py \\"
    echo "       --reads /path/to/reads.fasta \\"
    echo "       --genome /path/to/genome.fasta \\"
    echo "       --mode gapfiller \\"
    echo "       --flag all \\"
    echo "       -o /path/to/output \\"
    echo "       --batch 10"
    echo ""
    echo "5. Get help:"
    echo "   singularity exec degap.sif python /opt/degap/DEGAP.py --help"
    echo ""
    print_status "Note: Make sure to bind mount your data directories when running:"
    echo "   singularity exec --bind /your/data/path:/data degap.sif python /opt/degap/DEGAP.py ..."
}

# Main execution
main() {
    echo "========================================"
    echo "DEGAP v2.0 Singularity Container Builder"
    echo "========================================"
    echo ""
    
    # Parse command line arguments
    case "${1:-build}" in
        "build")
            check_singularity
            check_definition_file
            check_source_files
            build_container
            test_container
            show_usage
            ;;
        "test")
            check_singularity
            test_container
            ;;
        "usage")
            show_usage
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [build|test|usage|help]"
            echo ""
            echo "Commands:"
            echo "  build   - Build the DEGAP container (default)"
            echo "  test    - Test an existing container"
            echo "  usage   - Show usage examples"
            echo "  help    - Show this help message"
            ;;
        *)
            print_error "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
