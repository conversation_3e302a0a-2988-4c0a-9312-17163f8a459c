Bootstrap: docker
From: ubuntu:20.04

%labels
    Author DEGAP Development Team
    Version 2.0
    Description DEGAP v2.0 - Dynamic Elongation of a Genome Assembly Path

%help
    DEGAP v2.0 Container
    
    This container includes DEGAP v2.0 and all its dependencies for gap filling in genome assemblies.
    
    Usage examples:
    # GapFiller mode
    singularity exec degap.sif python /opt/degap/DEGAP.py --mode gapfiller \
        --seqleft /path/to/left.fasta \
        --seqright /path/to/right.fasta \
        --reads /path/to/reads.fasta \
        -o /path/to/output \
        --flag left
    
    # CtgLinker mode  
    singularity exec degap.sif python /opt/degap/DEGAP.py --mode ctglinker \
        --ctgseq /path/to/contigs.fasta \
        --reads /path/to/reads.fasta \
        --out /path/to/output \
        --filterDepth 0.3
    
    # TelSeeker mode
    singularity exec degap.sif python /opt/degap/DEGAP.py --mode telseeker \
        --reads /path/to/reads.fasta \
        --seqleft /path/to/start.fasta \
        --seqright /path/to/end.fasta \
        --flag left \
        -o /path/to/output

%environment
    export PATH="/opt/venv/bin:/opt/minimap2:/opt/hifiasm:/opt/samtools/bin:/opt/seqkit:/opt/mummer/bin:$PATH"
    export PYTHONPATH="/opt/degap:$PYTHONPATH"
    export LC_ALL=C

%post
    # Set non-interactive mode for apt
    export DEBIAN_FRONTEND=noninteractive
    
    # Update system and install basic dependencies including Python packages
    apt-get update && apt-get install -y \
        wget \
        curl \
        git \
        build-essential \
        gcc \
        g++ \
        make \
        cmake \
        zlib1g-dev \
        libbz2-dev \
        liblzma-dev \
        libncurses5-dev \
        libcurl4-gnutls-dev \
        libssl-dev \
        python3 \
        python3-pip \
        python3-dev \
        python3-setuptools \
        python3-wheel \
        python3-venv \
        python3-numpy \
        python3-scipy \
        python3-matplotlib \
        unzip \
        bzip2 \
        ca-certificates \
        && rm -rf /var/lib/apt/lists/*

    # Create a Python virtual environment and install packages
    python3 -m venv /opt/venv
    . /opt/venv/bin/activate

    # Upgrade pip
    pip install --no-cache-dir --upgrade pip

    # Install Python packages using pip
    pip install --no-cache-dir \
        biopython==1.80 \
        pysam==0.20.0
    
    # Install minimap2 (version 2.17-r941)
    cd /opt
    wget https://github.com/lh3/minimap2/releases/download/v2.17/minimap2-2.17_x64-linux.tar.bz2
    tar -xjf minimap2-2.17_x64-linux.tar.bz2
    mv minimap2-2.17_x64-linux minimap2
    rm minimap2-2.17_x64-linux.tar.bz2
    
    # Install hifiasm (version 0.16.1-r375)
    cd /opt
    git clone https://github.com/chhylp123/hifiasm.git
    cd hifiasm
    git checkout 0.16.1
    make
    cd /opt
    
    # Install SAMtools (version 1.6)
    cd /opt
    wget https://github.com/samtools/samtools/releases/download/1.6/samtools-1.6.tar.bz2
    tar -xjf samtools-1.6.tar.bz2
    cd samtools-1.6
    ./configure --prefix=/opt/samtools
    make && make install
    cd /opt
    rm samtools-1.6.tar.bz2
    
    # Install seqkit (version 2.8.0)
    cd /opt
    wget https://github.com/shenwei356/seqkit/releases/download/v2.8.0/seqkit_linux_amd64.tar.gz
    tar -xzf seqkit_linux_amd64.tar.gz
    mkdir -p seqkit
    mv seqkit seqkit/
    rm seqkit_linux_amd64.tar.gz
    
    # Install MUMmer (version 4.0.0beta2)
    cd /opt
    wget https://github.com/mummer4/mummer/releases/download/v4.0.0beta2/mummer-4.0.0beta2.tar.gz
    tar -xzf mummer-4.0.0beta2.tar.gz
    cd mummer-4.0.0beta2
    ./configure --prefix=/opt/mummer
    make && make install
    cd /opt
    rm mummer-4.0.0beta2.tar.gz
    
    # Create DEGAP directory and copy source files
    mkdir -p /opt/degap
    
    # Clean up
    apt-get clean
    pip cache purge

%files
    # Copy all DEGAP source files to the container
    AutoGapfiller.py /opt/degap/
    CtgLinker.py /opt/degap/
    DEGAP.py /opt/degap/
    FindExtensionContigs.py /opt/degap/
    FindExtensionReads.py /opt/degap/
    GapFiller.py /opt/degap/
    GapFillerClass.py /opt/degap/
    N50Check.py /opt/degap/
    README.md /opt/degap/
    TelSeeker.py /opt/degap/
    selectRawReads.py /opt/degap/
    DEGAP.png /opt/degap/

%runscript
    echo "DEGAP v2.0 Container"
    echo "Usage: singularity exec degap.sif python /opt/degap/DEGAP.py [options]"
    echo "For help: singularity exec degap.sif python /opt/degap/DEGAP.py --help"
    exec "$@"

%test
    # Test if all required tools are available
    echo "Testing DEGAP container..."
    
    # Test Python and packages
    python3 -c "import Bio; print('Biopython version:', Bio.__version__)"
    python3 -c "import pysam; print('Pysam version:', pysam.__version__)"
    
    # Test external tools
    minimap2 --version
    /opt/hifiasm/hifiasm --version
    samtools --version
    seqkit version
    nucmer --version
    
    # Test DEGAP script
    python3 /opt/degap/DEGAP.py --help
    
    echo "All tests passed!"
